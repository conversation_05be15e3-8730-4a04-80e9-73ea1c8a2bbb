# Development Guide

## Overview
This is the central index for all development documentation. Always check this guide before starting any task.

## Documentation Structure

### Database Documentation
- [Database Overview](database/overview.md) - General database architecture
- [Drive System MVP Schema](database/drive-system-mvp-schema.md) - Simplified MVP schema for startup
- [Drive System Schema](database/drive-system-schema.md) - Complete database schema for file drive solution
- [Drive System Comparison](database/drive-system-comparison.md) - MVP vs Full system comparison guide
- [UUID Benefits](database/uuid-benefits.md) - Why UUIDs are essential for file system security

### Features Documentation
- [Drive System](features/drive-system.md) - Complete file management solution with versioning and security
- [Job Status System](features/job-status-system.md) - Job application status management
- [Language Management System](features/language-management-system.md) - Multi-language support
- [Settings System](features/settings-system.md) - Application configuration management
- [User Management](features/user-management.md) - User accounts and authentication
- [OnlyOffice Troubleshooting](features/onlyoffice-troubleshooting.md) - Debug OnlyOffice integration issues

### UI/UX Documentation
- [Layout Patterns](ui/layout-patterns.md) - Standard layout components
- [Public Header Improvements](ui/public-header-improvements.md) - Header design patterns
- [View Structures](ui/view-structures.md) - View architecture patterns

### Implementation Summaries
- [Language Management Implementation](implementation/language-management-implementation-summary.md)
- [Profile System Implementation](implementation/profile-system-implementation-summary.md)
- [Public Header Improvements](implementation/public-header-improvements-summary.md)
- [Settings System Implementation](implementation/settings-system-implementation-summary.md)
- [User Management Implementation](implementation/user-management-implementation-summary.md)
- [User Registration Implementation](implementation/user-registration-implementation-summary.md)

### Forms Documentation
- [Form Submission](forms/submission.md) - Form handling patterns

### User Guides
- [Profile Management System](profile-management-system.md) - User profile management
- [Profile Login Page](profile-login-page.md) - Login system documentation

### Workflow Documentation
- [Development Workflow](workflow/) - Development processes and guidelines

## Documentation Standards

### Critical Path Requirements
- Always follow full documentation process for critical features
- Check for documentation conflicts before implementation
- Update docs before writing code

### Security Documentation
- Government-level security standards
- Encryption and audit requirements
- Access control documentation

### File Management
- Store technical docs in `/docs` directory
- Use file-specific naming: `docs/<module>/<name>.md`
- Reference docs in code comments: `[doc-ref:filename]`

## Quick Reference Links

### New Features
For new features, create documentation with:
- Implementation overview
- API/data structure examples
- Integration diagrams
- Security considerations

### Documentation Format
- Use clear header hierarchy (## Major, ### Minor)
- Numbered steps for processes
- Code blocks with language tags
- **Bold** for glossary terms
- Avoid nested lists deeper than 3 levels 