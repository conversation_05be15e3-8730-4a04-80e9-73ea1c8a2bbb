<?php
namespace App\Libraries;

use Illuminate\Database\Capsule\Manager as DB;
use App\Models\File;

class Storage
{
	
	public $path;
	public $id;
	public $error;
	public $file;
	public $original_name;

	public $status;
	public $message;
	public $target;
	public $resize_status;

	public $useFTP = true;

	/**
	 * Get storage configuration value with database fallback
	 */
	private function getStorageConfig($key, $envKey = null, $default = null) {
		$envKey = $envKey ?: "storage.{$key}";
		return get_option("storage_{$key}", env($envKey, $default));
	}

	/**
	 * Initialize storage configuration
	 */
	private function initializeStorageConfig() {
		$this->useFTP = $this->getStorageConfig('use_ftp', 'storage.use_ftp', '0') == '1';
	}

	/**
	 * Convert size string to bytes
	 */
	private function convertToBytes($size) {
		$size = trim($size);
		$unit = strtolower(substr($size, -1));
		$value = (int) $size;

		switch ($unit) {
			case 'g':
				$value *= 1024;
			case 'm':
				$value *= 1024;
			case 'k':
				$value *= 1024;
		}

		return $value;
	}

	public  function save($file,$public=0){

		// Initialize storage configuration
		$this->initializeStorageConfig();

		$save_to = $this->getStorageConfig('path', 'storage.path', WRITEPATH . 'uploads');

		$save_to = rtrim($save_to, '/');

		$errors=[];
		$error=false;

		if (
	        !isset($file['error']) ||
	        is_array($file['error'])
	    ) {
	        $error = true;$errors[]=('Invalid parameters.');
	    }

	    $filepath="";


	    switch ($file['error']) {
	        case UPLOAD_ERR_OK:
	            break;
	        case UPLOAD_ERR_NO_FILE:
	            $error = true;$errors[]=tr('No file sent.');
	        case UPLOAD_ERR_INI_SIZE:
	        case UPLOAD_ERR_FORM_SIZE:
	            $error = true;$errors[]=tr('Exceeded filesize limit.');
	        default:
	            $error = true;$errors[]=tr('Unknown errors.');
	    }

	    // Get allowed extensions and validate
	    $extensions = $this->getStorageConfig('extensions', 'storage.extensions', 'pdf,jpeg,png,jpg');
	    $allowed_extensions = array_map('trim', explode(',', strtolower($extensions)));
	    
	    $temp = explode(".", $file["name"]);
	    $file_extension = strtolower(end($temp));
	    
	    // Validate file extension
	    if (!in_array($file_extension, $allowed_extensions)) {
	        $error = true;
	        $errors[] = tr('File type not allowed. Allowed types: ') . $extensions;
	    }

	    // Validate file size
	    $max_size = $this->getStorageConfig('max_size', 'storage.max_size', '2M');
	    $max_size_bytes = $this->convertToBytes($max_size);
	    
	    if ($file['size'] > $max_size_bytes) {
	        $error = true;
	        $errors[] = tr('File size exceeds limit. Maximum allowed: ') . $max_size;
	    }

	    $old_file_name=$file["name"];

	  
	    $file_id=0;
	    $rand=rand(1, 1000000).round(microtime(true)) ;
	    $file_name = $rand . '.' . end($temp);
	    $target =  $save_to.'/'.basename($file_name);



	    if ($this->useFTP) {

	    	try {
	    		$ftp_conn = $this->initializeFTP();
	    		ftp_pasv($ftp_conn, true);

	    		if (!ftp_put($ftp_conn, $target, $file['tmp_name'], FTP_BINARY)) {
	    			$error = true;
	    			$errors[] = tr('Failed to upload file to FTP.');
	    		}
	    		ftp_close($ftp_conn);
	    	} catch (\Exception $e) {
	    		$error = true;
	    		$errors[] = tr('FTP Error: ') . $e->getMessage();
	    	}
	    } else {
	        if (!move_uploaded_file($file['tmp_name'], $target)) {
	            $error = true;
	            $errors[] = tr('Failed to move uploaded file.');
	        }
	    }

	    if (!$error) {
	    	$file  = new File;
	    	$file = $file->create([
	    		"name"=>$old_file_name,
	    		"file"=>$file_name,
	    	]);
	        
	        $file_id = $file->id;
	    }
	    
	    
		$this->status  =!$error;
		$this->message =$errors;
		$this->file    =$file_name;
		$this->path    =$target;
		$this->original_name    =$old_file_name;
		$this->id      =$file_id;
		$this->target  =$target;

	    return $this;
	}


	public  function saved(){
		
		return (bool)$this->status;
	}

	public  function errors(){
		return $this->message;
	}



	public function remove(){

	}

	public function input($conf=[]){
		$name="file";

		$config = $conf;

		$id="file-".rand(10000000,999999999);

		if (isset($config["id"])) {
	  		$id=$config["id"];
	  		unset($config["id"]);
	  	}


	  	if (isset($config["name"])) {
	  		$name=$config["name"];
	  		unset($config["name"]);
	  	}

	  	$onchange="$('#has_".$id."').val('true');";
	  	if (isset($config["onchange"])) {
	  		$onchange.=$config["onchange"];
	  		unset($config["onchange"]);
	  	}


	  	$attrs = "";
		$array_keys = array_keys($config);
		foreach ($array_keys as $key) {
		  $attrs.=$key.'="'.$config[$key].'" ';
		}


		$html = '';
		$html.= '<input type="file" name="'.$name.'" id="'.$id.'" '. $attrs.' onchange="'.$onchange.'">';
		$html.= '<input type="text"  name="has_'.$name.'" id="has_'.$id.'" value="false" hidden>';

		return $html;
	}

	public function get($file,$type=""){

		// Initialize storage configuration
		$this->initializeStorageConfig();

		$uploads_path = $this->getStorageConfig('path', 'storage.path', WRITEPATH . 'uploads');

		if ($this->useFTP) {
	        $ftp_conn = $this->initializeFTP();
	    }

		$uploads_path = rtrim($uploads_path, '/');

		if (is_numeric($file)) {

			$file_data = File::find($file);

		}else{
			$file_data = File::where("file",$file)->first();
		}


		if (!$file_data) {
			return false;
		}

		if ($this->useFTP) {

			try {
				$remote_file = $uploads_path . '/' . $file_data->file;

				if (ftp_size($ftp_conn, $remote_file) != -1) {
					ftp_close($ftp_conn);
					return base_url("files/" . urlencode($file_data->file));
				} 

				ftp_close($ftp_conn);
			} catch (\Exception $e) {
				if (isset($ftp_conn)) {
					ftp_close($ftp_conn);
				}
			}

			return false;
	    }

		if ($file_data) {
			return base_url("files/".urlencode($file_data['file']));
		}

		return false;
	}

	public function read($fileName) {

		// Initialize storage configuration
		$this->initializeStorageConfig();

		$save_to = $this->getStorageConfig('path', 'storage.path', WRITEPATH . 'uploads');
		$save_to = rtrim($save_to, '/');

		// Handle FTP storage
		if ($this->useFTP) {
			try {
				$ftp_conn = $this->initializeFTP();
				ftp_pasv($ftp_conn, true);

				// The path where the file is stored on the FTP server
				$remoteFilePath = $save_to.'/' . $fileName;

				// Temporary file to store the downloaded data
				$tempHandle = fopen('php://temp', 'r+');

				if (!ftp_fget($ftp_conn, $tempHandle, $remoteFilePath, FTP_BINARY, 0)) {
					fclose($tempHandle);
					ftp_close($ftp_conn);
					return false; // Failed to download file
				}

				// If successful, read the file content from the temp stream
				rewind($tempHandle);
				$fileContent = stream_get_contents($tempHandle);
				fclose($tempHandle);
				ftp_close($ftp_conn);

				return $fileContent;
			} catch (\Exception $e) {
				if (isset($tempHandle)) {
					fclose($tempHandle);
				}
				if (isset($ftp_conn)) {
					ftp_close($ftp_conn);
				}
				return false;
			}
		}

		// Handle local file storage
		$localFilePath = $save_to . '/' . $fileName;
		
		if (!file_exists($localFilePath)) {
			return false;
		}

		return file_get_contents($localFilePath);
    }



	private function initializeFTP() {
	    $ftp_server = $this->getStorageConfig('ftp_server', 'storage.ftp_server');
	    $ftp_username = $this->getStorageConfig('ftp_username', 'storage.ftp_username');
	    $ftp_password = $this->getStorageConfig('ftp_password', 'storage.ftp_password');

	    // Validate FTP credentials
	    if (empty($ftp_server) || empty($ftp_username) || empty($ftp_password)) {
	        throw new \Exception('FTP credentials are not configured properly.');
	    }

	    // Establish the FTP connection.
	    $ftp_conn = ftp_connect($ftp_server);

	    if (!$ftp_conn) {
	        throw new \Exception('Failed to connect to FTP server: ' . $ftp_server);
	    }

	    // Login to the FTP server.
	    $login = ftp_login($ftp_conn, $ftp_username, $ftp_password);

	    if (!$login) {
	        ftp_close($ftp_conn);
	        throw new \Exception('FTP authentication failed for user: ' . $ftp_username);
	    }

	    return $ftp_conn;
	}
}