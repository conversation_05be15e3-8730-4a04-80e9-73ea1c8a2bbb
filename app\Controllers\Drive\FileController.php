<?php

namespace App\Controllers\Drive;

use App\Controllers\BaseController;
use App\Models\Drive\Directory;
use App\Models\Drive\File;
use App\Models\Drive\ActivityLog;
use App\Libraries\Storage;

class FileController extends BaseController
{
    /**
     * Upload files
     */
    public function upload()
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Directory") => ["directory_id", "in_table:directories,id"],
            tr("File") => ["file", "required|uploaded_file:0,".get_option("storage_max_size").",".get_option("storage_extensions")]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()->all()
            ]);
        }

        $user = auth();
        $directoryId = input('directory_id');

        $directory_path = get_option("storage_path");
        

        if($directoryId ){
            // Validate directory
            $directory = Directory::find($directoryId);
            if (!$directory || !$directory->canAccess($user->id)) {
                return _response([
                    'success' => false,
                    'message' => ['Invalid directory or access denied']
                ]);
            }

            $directory_path = $directory->path;
        }

        

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            return _response([
                'success' => false,
                'message' => ['File upload failed']
            ]);
        }
        
        $file_id = uuid();

        $extension = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);

        // Create file record
        $file = new File([
            'id' => $file_id,
            'name' => $_FILES['file']['name'],
            'original_name' => $_FILES['file']['name'],
            'directory_id' => $directoryId,
            'user_id' => $user->id,
            'file_path' => $directory_path . "/" . $file_id.".".$extension,
            'mime_type' => $_FILES['file']['type'],
            'size_bytes' => $_FILES['file']['size'],
            
            'status' => File::STATUS_ACTIVE
        ]);

        // die($directory_path . "/" . $file_id);

        file_put_contents($directory_path . "/" . $file_id.".".$extension, file_get_contents($_FILES['file']['tmp_name']));

       

        

        $file->hash_sha256 = hash_file('sha256', $directory_path . "/" . $file_id.".".$extension);
        $file->save();

        // Update directory size

        if($directoryId){
            $directory->updateSize();
        }

        // Log activity
        ActivityLog::logFileUpload($file->id, $user->id, "Uploaded file: {$file->original_name}");

        return _response([
            'success' => true,
            'message' => ['File uploaded successfully'],
            'action' => 'reload',
            'data' => [
                'file' => $file
            ]
        ]);
    }

    /**
     * Download a file
     */
    public function download($fileId)
    {
        if (!auth()) {
            return _error_code(401);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || !$file->canAccess($user->id)) {
            return _error_code(404);
        }

        // Increment download count
        $file->incrementDownloadCount();

        // Log activity
        ActivityLog::logFileDownload($file->id, $user->id, "Downloaded file: {$file->name}");

        // Use storage library to serve file
        $storage = new Storage();
        $fileContent = $storage->read($file->file_path);

        if ($fileContent === false) {
            return _error_code(404);
        }

        // Set headers for download
        $this->response->setHeader('Content-Type', $file->mime_type);
        $this->response->setHeader('Content-Length', strlen($fileContent));
        $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $file->name . '"');
        $this->response->setBody($fileContent);

        return $this->response;
    }

    /**
     * View/preview a file
     */
    public function view($fileId)
    {
        if (!auth()) {
            return _error_code(401);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || !$file->canAccess($user->id)) {
            return _error_code(404);
        }

        // Log activity
        ActivityLog::logFileView($file->id, $user->id, "Viewed file: {$file->name}");

        // Use storage library to serve file
        $storage = new Storage();
        $fileContent = $storage->read($file->file_path);

        if ($fileContent === false) {
            return _error_code(404);
        }

        // Set headers for inline viewing
        $this->response->setHeader('Content-Type', $file->mime_type);
        $this->response->setHeader('Content-Length', strlen($fileContent));
        $this->response->setHeader('Content-Disposition', 'inline; filename="' . $file->name . '"');
        $this->response->setBody($fileContent);

        return $this->response;
    }

    /**
     * Delete a file
     */
    public function delete($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        $fileName = $file->name;
        $directory = $file->directory;

        // Soft delete the file
        $file->delete();

        // Update directory size
        if ($directory) {
            $directory->updateSize();
        }

        // Log activity
        ActivityLog::logActivity('DELETE', $user->id, $file->id, null, "Deleted file: {$fileName}");

        return _response([
            'success' => true,
            'message' => ['File deleted successfully']
        ]);
    }

    /**
     * Rename a file
     */
    public function rename($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("File name") => ["name", "required|max_length[255]"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        $newName = input('name');
        $oldName = $file->name;

        // Check if file with same name already exists in directory
        $existingFile = File::where('name', $newName)
            ->where('directory_id', $file->directory_id)
            ->where('user_id', $user->id)
            ->where('id', '!=', $fileId)
            ->first();

        if ($existingFile) {
            return _response([
                'success' => false,
                'message' => ['File with this name already exists in this directory']
            ]);
        }

        // Update file name
        $file->name = $newName;
        $file->save();

        // Log activity
        ActivityLog::logActivity('RENAME', $user->id, $file->id, null, "Renamed file from '{$oldName}' to '{$newName}'");

        return _response([
            'success' => true,
            'message' => ['File renamed successfully'],
            'data' => [
                'file' => $file
            ]
        ]);
    }

    /**
     * Move a file to another directory
     */
    public function move($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Target directory") => ["target_directory_id", "required"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);
        $targetDirectoryId = input('target_directory_id');

        if (!$file || $file->user_id != $user->id) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        // Validate target directory
        $targetDirectory = Directory::find($targetDirectoryId);
        if (!$targetDirectory || !$targetDirectory->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['Invalid target directory']
            ]);
        }

        // Check if file with same name already exists in target directory
        $existingFile = File::where('name', $file->name)
            ->where('directory_id', $targetDirectoryId)
            ->where('user_id', $user->id)
            ->where('id', '!=', $fileId)
            ->first();

        if ($existingFile) {
            return _response([
                'success' => false,
                'message' => ['File with this name already exists in target directory']
            ]);
        }

        // Move file
        $operation = $file->moveTo($targetDirectoryId, $user->id);

        // Log activity
        ActivityLog::logActivity('MOVE', $user->id, $file->id, null, "Moved file to: {$targetDirectory->name}");

        return _response([
            'success' => true,
            'message' => ['File moved successfully'],
            'data' => [
                'file' => $file,
                'operation' => $operation
            ]
        ]);
    }

    /**
     * Copy a file to another directory
     */
    public function copy($fileId)
    {
        if (!auth()) {
            return _response([
                'success' => false,
                'message' => ['Authentication required']
            ]);
        }

        $validation = validate([
            tr("Target directory") => ["target_directory_id", "required"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        $user = auth();
        $file = File::find($fileId);
        $targetDirectoryId = input('target_directory_id');

        if (!$file || !$file->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['File not found or access denied']
            ]);
        }

        // Validate target directory
        $targetDirectory = Directory::find($targetDirectoryId);
        if (!$targetDirectory || !$targetDirectory->canAccess($user->id)) {
            return _response([
                'success' => false,
                'message' => ['Invalid target directory']
            ]);
        }

        // Generate new name if file exists in target
        $newName = $file->name;
        $counter = 1;
        while (File::where('name', $newName)
                   ->where('directory_id', $targetDirectoryId)
                   ->where('user_id', $user->id)
                   ->exists()) {
            $pathInfo = pathinfo($file->name);
            $baseName = $pathInfo['filename'];
            $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
            $newName = $baseName . " (Copy {$counter})" . $extension;
            $counter++;
        }

        // Create copy
        $copiedFile = new File([
            'name' => $newName,
            'original_name' => $file->original_name,
            'directory_id' => $targetDirectoryId,
            'user_id' => $user->id,
            'file_path' => $file->file_path, // Same physical file
            'mime_type' => $file->mime_type,
            'size_bytes' => $file->size_bytes,
            'hash_sha256' => $file->hash_sha256,
            'status' => File::STATUS_ACTIVE
        ]);

        $copiedFile->save();

        // Update target directory size
        $targetDirectory->updateSize();

        // Log activity
        ActivityLog::logActivity('COPY', $user->id, $copiedFile->id, null, "Copied file to: {$targetDirectory->name}");

        return _response([
            'success' => true,
            'message' => ['File copied successfully'],
            'data' => [
                'file' => $copiedFile
            ]
        ]);
    }
}
