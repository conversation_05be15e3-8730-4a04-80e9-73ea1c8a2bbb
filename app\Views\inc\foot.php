<footer class="footer mt-auto py-3 pt-5">
  <div class="container text-center">
    <span class="text-muted">© <?= date("Y") ?> <?= get_option("app_name") ?> </span>
  </div>
</footer>

<script>

function createDatatable(selector,  showlength = true, extraOptions = {}) {
  const defaultOptions = {
  	 language: {
      search: '<span></span> _INPUT_',
      searchPlaceholder: '<?= tr("Search") ?>',
      lengthMenu: '<span><?= tr("Show") ?>:</span> _MENU_',
      paginate: { 'first': '<?= tr("First") ?>', 'last': '<?= tr("Last") ?>', 'next': '<?= tr("Next") ?>', 'previous': '<?= tr("Previous") ?>' },
  	},
  	autoWidth: false,
    dom: '<"datatable-header"fB' + (showlength ? 'l' : '') + '><"datatable-scroll-wrap"t><"datatable-footer"ip>',
    stateSave: true,
    responsive:true,
    searchDelay:700,
     buttons: {           
        buttons: [],
    },           
  };

  if ('ajax' in extraOptions) {
    defaultOptions.processing = true;
    defaultOptions.serverSide = true;
  }

  const options = {...defaultOptions, ...extraOptions};

  $(selector).DataTable(options);
}

function initDatatable() {
    
    $.each($.fn.dataTable.tables(true), function() {
        
       
        let table = $(this).DataTable();
        let oldOptions = table.settings()[0].oInit;
        
        
        $(this).data('oldOptions', oldOptions);

        table.destroy();
        
        let selector = '#' + $(this).attr('id');
        let storedOptions = $(this).data('oldOptions');

        createDatatable(selector, true, storedOptions);
    });
}


		
   $(document).ready(function() {
   		var datatables=[];
            $('table.datatable').each(function(index, el) {
            
                responsive = $(el).attr("aresponsive")?$(el).attr("aresponsive"):true;

                
                processing=false;
                autoWidth=$(el).attr("aautoWidth")?$(el).attr("aautoWidth"):false;

                buttons = $(el).attr("abuttons")?$(el).attr("abuttons").replace(/\[|\]/g,'').split(','):[];

                print_columns = null;
                hide=$(el).attr("ahide")?$(el).attr("ahide"):[]
                unsort=$(el).attr("aunsort")?$(el).attr("aunsort"):[]
                showlength=$(el).attr("showlength")?($(el).attr("showlength")=="false"?false:true):true;
                length=$(el).attr("length")?($(el).attr("length").split(",")):[25,50, 100];
                order=$(el).attr("aorder")?JSON.parse($(el).attr("aorder").replace(/'/g, '"')):[ 0, "desc" ]
                console.log($(el).attr("showlength"))
                console.log(unsort)
                tableajax=null;

                var length = length.map(function (x) { 
				  return parseInt(x, 10); 
				});
                

                tablebuttons = []
                for (var i = 0; i < buttons.length; i++) {
                    if (buttons[i]=="colvis") {
                        tablebuttons.push({
                            extend: 'colvis',
                              text: '<i class="icon-grid3"></i>',
                              className: 'btn bg-indigo-400 btn-icon dropdown-toggle',
                              postfixButtons: ['colvisRestore'],
                        })
                    }

                    if (buttons[i]=="print") {
                        tablebuttons.push({
                        
                            extend: 'print', 
                            className: 'btn btn-light border',
                             exportOptions: {
                                stripHtml: true,
                                columns: print_columns
                            }
                    
                        })
                    }

                    if (buttons[i]=="copyHtml5") {
                    	tablebuttons.push({
                        
                            extend: 'copyHtml5', 
                            className: 'btn btn-light border',
                             exportOptions: {
                                stripHtml: true,
                                columns: print_columns
                            }
                    
                        })
                    }
                }

                if ($(el).hasClass("ajax")) {
                	url = $(el).data("aurl")?$(el).data("aurl"):"";
                    tableajax={
                        'url' : url, 
                        type: "post",  
                    }
	                

	                processing=true;

	                 
                }

                
                datatables[index] = $(el).dataTable({
                    "lengthMenu": [
                      length,
                      length
                    ],
                    "order": [order],
                        language: {
                      search: '<span></span> _INPUT_',
                      searchPlaceholder: '<?= tr("Search") ?>',
                      lengthMenu: '<span><?= tr("Show") ?>:</span> _MENU_',
                      paginate: { 'first': '<?= tr("First") ?>', 'last': '<?= tr("Last") ?>', 'next': '<?= tr("Next") ?>', 'previous': '<?= tr("Previous") ?>' }
                  },
                    "columnDefs": [
                        { "orderable": false, "targets": unsort},
                        
                    ],
                    dom: '<"datatable-header"fB'+(showlength?'l':'')+'><"datatable-scroll-wrap"t><"datatable-footer"ip>',
                    responsive: responsive,
                     autoWidth: autoWidth,
                    processing: processing,
                    stateSave: true,
                    serverSide: $(el).hasClass("ajax"),
                    ajax: tableajax,
                    buttons: {
                     
                        
                         
                        buttons: tablebuttons,
                    },
                
                    




                })
            });

           
			$("body ").on('submit','form.ajax', function(event) {
			
				event.preventDefault()

				var form = $(this);
				var action = form.attr("action")?form.attr("action"):"";
				var method = form.attr("method")?form.attr("method"):"post";

				var submit_button = form.find('button[type!=button]').attr('name');
					
				var formData = new FormData(this);

				if (submit_button) {
					formData.append(submit_button, 1);
				}

				formData.append("ajax_form", 1);
				
				if (method.toLowerCase()=="get") {
					
					for(var pair of formData.entries()) {
			
					   action = addParams(action,pair)
					}

					location.href=action
				}else{
					var submit_button2 = form.find('button[type!=button]')
					$(submit_button2).prop('disabled', true)
					btntxt = $(submit_button2).text();
					$(submit_button2).empty()
					 $(submit_button2).append('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');

                    ajax_config = {
                        url: action,
                        type: method,
                        dataType: 'json',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function(data){
                            $(submit_button2).prop('disabled', false)
                            $(submit_button2).empty()
                            $(submit_button2).text(btntxt)
                            
                            $(document).trigger('submit_complete', data);
                        }
                    };


                    if(method.toLowerCase()=="post" && formData.has("file")){
                        ajax_config.xhr = function() {
                            const xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener('progress', function(evt) {
                                if (evt.lengthComputable) {
                                    const percentComplete = (evt.loaded / evt.total) * 100;
                                    form.find('.progress-bar').css('width', percentComplete + '%');
                                }
                            }, false);
                            return xhr;
                        };
                    }
					$.ajax(ajax_config);
				}
		
			});
		

		const uniqId = (() => {
		    let i = 0;
		    return () => {
		        return (i++)+"tk";
		    }
		})();

        
     



	        window.onbeforeunload = function(){
			  doswal({
				  title: '<?= tr("Please wait") ?>',
				  html: '<br><div class="spinner-border" style="width: 3rem; height: 3rem;" role="status"><span class="sr-only">Loading...</span></div>',
				  timer: 10000,
				  showConfirmButton: false,
				  timerProgressBar: true,
			  });
			};




        });


  $( window ).on( "load", function() {
       initSelect2();
    });

   $(document).ready(function() {


   	$("input[type=number]").focus( function(event) {
		$(this).select()
	});




    var elems = Array.prototype.slice.call(document.querySelectorAll('.form-check-input-switchery'));
    elems.forEach(function(html) {
      var switchery = new Switchery(html);
    });


   });

   


</script>









