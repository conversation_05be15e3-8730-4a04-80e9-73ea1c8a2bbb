# OnlyOffice Troubleshooting Guide

## Overview
This guide helps troubleshoot common OnlyOffice integration issues, particularly the "document cannot be saved" error.

## Common Issues

### 1. "Document Cannot Be Saved" Error

This error typically occurs when OnlyOffice Document Server cannot successfully call back to your application to save the document.

#### Possible Causes:
- **Callback URL not accessible**: OnlyOffice server cannot reach your application
- **JWT signature issues**: Token verification failing
- **File permission problems**: Cannot write to file storage
- **Network connectivity**: Firewall or network issues

#### Debugging Steps:

##### Step 1: Check Callback URL Accessibility
```bash
# Test if callback URL is accessible from OnlyOffice server
curl -X POST http://your-app.com/drive/onlyoffice/test-callback
```

Expected response:
```json
{
  "status": "ok",
  "message": "Callback URL is accessible",
  "timestamp": 1234567890,
  "ip": "127.0.0.1"
}
```

##### Step 2: Check Application Logs
Check `writable/logs/` for OnlyOffice-related errors:
```bash
# Look for OnlyOffice logs
grep -i "onlyoffice" writable/logs/log-*.log
```

Common log entries:
- `OnlyOffice callback received for file: X` - Callback reached
- `OnlyOffice: Document is ready for saving` - Save initiated  
- `OnlyOffice: Successfully saved document` - Save completed
- `OnlyOffice callback: Signature verification failed` - JWT issues

##### Step 3: Debug Document Configuration
Access debug endpoint (admin only):
```
GET /drive/onlyoffice/debug/{file_id}
```

This returns configuration details including:
- File permissions
- JWT token configuration
- Callback URLs
- File paths

##### Step 4: Check File Permissions
Ensure web server can read/write files:
```bash
# Check file permissions
ls -la storage_path/

# Fix permissions if needed
chmod 755 storage_path/
chown -R www-data:www-data storage_path/
```

##### Step 5: Test Without JWT
If using a secret key, temporarily disable it:
1. Go to Settings → OnlyOffice
2. Clear the "OnlyOffice Secret Key" field
3. Save and test document editing

### 2. JWT Signature Verification Failed

#### Symptoms:
- Documents open but cannot save
- Log shows: `OnlyOffice callback: Signature verification failed`

#### Solutions:
1. **Verify Secret Key**: Ensure the same secret is configured in both OnlyOffice Document Server and your application
2. **Check JWT Generation**: Ensure JWT tokens are properly formatted
3. **Test Without JWT**: Temporarily disable JWT to isolate the issue

#### OnlyOffice Document Server Configuration:
```json
{
  "services": {
    "CoAuthoring": {
      "secret": {
        "inbox": {
          "string": "your-secret-key-here"
        },
        "outbox": {
          "string": "your-secret-key-here"
        }
      }
    }
  }
}
```

### 3. File Permission Issues

#### Symptoms:
- Callback received but save fails
- Log shows: `Failed to save updated document to file path`

#### Solutions:
1. **Check Directory Permissions**:
   ```bash
   # Ensure directory is writable
   chmod 755 /path/to/storage
   ```

2. **Check File Ownership**:
   ```bash
   # Ensure web server owns files
   chown -R www-data:www-data /path/to/storage
   ```

3. **Test File Writing**:
   ```php
   // Test in PHP
   $testFile = '/path/to/storage/test.txt';
   $result = file_put_contents($testFile, 'test');
   var_dump($result); // Should return number of bytes written
   ```

### 4. Network Connectivity Issues

#### Symptoms:
- OnlyOffice editor loads but callback never received
- No callback logs in application

#### Solutions:
1. **Check Firewall Rules**: Ensure OnlyOffice server can reach your application
2. **Verify Network Configuration**: Check if both servers can communicate
3. **Test Direct Connection**:
   ```bash
   # From OnlyOffice server, test connectivity
   telnet your-app-server 80
   ```

4. **Check DNS Resolution**: Ensure hostnames resolve correctly

### 5. Docker/Container Issues

If using Docker for OnlyOffice Document Server:

#### Check Container Connectivity:
```bash
# Test from OnlyOffice container
docker exec only-office-container curl http://host.docker.internal/drive/onlyoffice/test-callback
```

#### Network Configuration:
```yaml
# docker-compose.yml
version: '3'
services:
  onlyoffice:
    image: onlyoffice/documentserver
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=your-secret-key
    networks:
      - app-network
```

## Configuration Examples

### Development Setup (No JWT)
```php
// Settings
onlyoffice_url: http://localhost:8080
onlyoffice_secret: (empty)
```

### Production Setup (With JWT)
```php
// Settings  
onlyoffice_url: https://onlyoffice.example.com
onlyoffice_secret: your-strong-secret-key-here
```

### Docker Development
```bash
# Quick start with Docker
docker run -i -t -d -p 8080:80 \
  -e JWT_ENABLED=false \
  onlyoffice/documentserver
```

## Testing Checklist

Before reporting issues, verify:

- [ ] OnlyOffice Document Server is running and accessible
- [ ] Application can reach OnlyOffice server
- [ ] OnlyOffice server can reach application callback URLs
- [ ] File permissions are correct
- [ ] JWT configuration matches on both sides (if used)
- [ ] Firewall allows communication between servers
- [ ] Application logs show callback attempts
- [ ] Test callback URL returns success response

## Common Log Patterns

### Successful Save:
```
OnlyOffice callback received for file: 123
OnlyOffice callback status: 2 for file: 123
OnlyOffice: Document is ready for saving
OnlyOffice: Downloaded document, size: 12345 bytes
OnlyOffice: Successfully saved document to: /path/file.docx
OnlyOffice: Document save completed successfully
```

### Failed Save (Permission):
```
OnlyOffice callback received for file: 123
OnlyOffice: Failed to save updated document to file path: /path/file.docx
```

### Failed Save (JWT):
```
OnlyOffice callback received for file: 123
OnlyOffice callback: Signature verification failed
```

### No Callback:
```
# No OnlyOffice callback logs = connectivity issue
```

## Getting Help

If issues persist:
1. Enable debug logging in CodeIgniter
2. Check both application and OnlyOffice server logs
3. Use the debug endpoint to verify configuration
4. Test with minimal setup (no JWT, local files)
5. Verify network connectivity between servers

## Related Documentation
- [OnlyOffice API Documentation](https://api.onlyoffice.com/editors/basic)
- [JWT Authentication Guide](https://api.onlyoffice.com/editors/signature)
- [Docker Installation](https://github.com/ONLYOFFICE/Docker-DocumentServer) 