<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= esc($file->name) ?> - <?= tr("Edit Document") ?></title>
    
    <!-- OnlyOffice Document Server API -->
    <script type="text/javascript" src="<?= $onlyOfficeUrl ?>/web-apps/apps/api/documents/api.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #495057;
        }
        
        .header .actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid #ccc;
            background: #fff;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #f8f9fa;
        }
        
        .btn-primary {
            background: #007bff;
            border-color: #007bff;
            color: #fff;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        #onlyoffice-editor {
            width: 100%;
            height: calc(100vh - 60px);
        }

        iframe{
            width: 100%;
            height: calc(100vh - 60px);
        }

        /* Hide OnlyOffice logo and branding */
        .asc-window .asc-window-header .asc-window-header-logo,
        .toolbar .logo,
        .header-logo,
        .brand-logo,
        .onlyoffice-logo,
        .asc-window-header .logo,
        .toolbar-header .logo,
        .header .logo,
        .top-toolbar .logo,
        .main-toolbar .logo,
        .document-title .logo,
        .header-brand,
        .brand,
        .logo-container,
        .header-logo-container,
        .toolbar-logo,
        .editor-logo,
        .app-logo,
        .title-logo,
        .header-title .logo,
        .toolbar-brand,
        .brand-container {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }

        /* Hide OnlyOffice watermark and footer branding */
        .statusbar .logo,
        .footer .logo,
        .watermark,
        .onlyoffice-watermark,
        .editor-watermark,
        .document-watermark,
        .footer-brand,
        .statusbar-brand {
            display: none !important;
            visibility: hidden !important;
        }

        /* Additional logo hiding for different OnlyOffice versions */
        [class*="logo"],
        [id*="logo"],
        [class*="brand"],
        [id*="brand"] {
            display: none !important;
        }

        /* Restore necessary elements that might be hidden by the above rules */
        .btn,
        .button,
        .toolbar-btn,
        .menu-item,
        .dropdown,
        .form-control,
        .input,
        .textarea {
            display: block !important;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh - 60px);
            font-size: 18px;
            color: #6c757d;
        }
        
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100vh - 60px);
            flex-direction: column;
            color: #dc3545;
        }
        
        .error h2 {
            margin-bottom: 10px;
        }
         section.logo{
            display: none !important;
        }

        .logo{
            display: none !important;
        }
        #header-logo{
            display: none !important;
        }
        #header-logo-container{
            display: none !important;
        }
        #header-logo-container{
            display: none !important;
        }
        #header-logo-container{
            display: none !important;
        }
        #header-logo-container{}
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>
            <i class="fas fa-edit"></i>
            <?= esc($file->name) ?>
        </h1>
        <div class="actions">
            <a href="<?= base_url("drive/{$file->directory_id}") ?>" class="btn">
                <?= tr("Back to Drive") ?>
            </a>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading" class="loading">
        <div>
            <div class="spinner-border" role="status">
                <span class="sr-only"><?= tr("Loading...") ?></span>
            </div>
            <p><?= tr("Loading document editor...") ?></p>
        </div>
    </div>

    <!-- Error State -->
    <div id="error" class="error" style="display: none;">
        <h2><?= tr("Failed to load editor") ?></h2>
        <p><?= tr("Please check your OnlyOffice configuration and try again.") ?></p>
        <a href="<?= base_url("drive/{$file->directory_id}") ?>" class="btn">
            <?= tr("Back to Drive") ?>
        </a>
    </div>

    <!-- OnlyOffice Editor Container -->
    <div id="onlyoffice-editor"></div>

    <script>
        let docEditor;
        
        // OnlyOffice configuration
        const config = <?= json_encode($config) ?>;

        console.log(config);
        
        // Initialize OnlyOffice editor
        window.onload = function() {
            try {
                docEditor = new DocsAPI.DocEditor("onlyoffice-editor", {
                    ...config,
                    events: {
                        onAppReady: function() {
                            console.log('OnlyOffice editor is ready');
                            document.getElementById('loading').style.display = 'none';

                    
                        },
                        onDocumentStateChange: function(event) {
                            console.log('Document state changed:', event);
                        },
                        onError: function(event) {
                            console.error('OnlyOffice error:', event);
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('error').style.display = 'flex';
                        },
                        onInfo: function(event) {
                            console.log('OnlyOffice info:', event);
                        },
                        onWarning: function(event) {
                            console.warn('OnlyOffice warning:', event);
                        }
                    }
                });
            } catch (error) {
                console.error('Failed to initialize OnlyOffice editor:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'flex';
            }
        };
        
        // Save document function
        function saveDocument() {
            if (docEditor) {
                docEditor.downloadAs();
            }
        }
        
        // Handle page unload
        window.addEventListener('beforeunload', function(e) {
            if (docEditor) {
                // OnlyOffice will handle auto-save
                return undefined;
            }
        });
        
        // Function to hide OnlyOffice logos
 
        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S or Cmd+S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                saveDocument();
            }
        });

  
    </script>
</body>
</html>
