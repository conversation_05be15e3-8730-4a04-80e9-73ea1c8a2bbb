<?php

namespace App\Controllers;



class Settings extends BaseController
{

    function __construct(){
       
        
        $this->d['nav']['active']="settings";
     }

    public function index()
    {

        if (!_can("settings")) {
            return _error_code(403);
        }

        if (is_post()) {

            // Handle storage connection test
            if (input('test_storage')) {
                return $this->testStorageConnection();
            }

            $v = validate([
                "application_status" =>["application_status","required|in:active,inactive"],
                "public_status" =>["public_status","required|in:active,inactive"],
                "app_name" =>["app_name","required|min:3|max:100"],
                "sms_user_id" =>["sms_user_id","max:100"],
                "sms_password" =>["sms_password","max:100"],
                "recaptcha_active" =>["recaptcha_active","required|in:0,1"],
                "recaptcha_secret" =>["recaptcha_secret","max:200"],
                "recaptcha_site" =>["recaptcha_site","max:200"],
                
                // Storage Configuration
                "storage_path" =>["storage_path","required|max:500"],
                "storage_ftp_server" =>["storage_ftp_server","max:200"],
                "storage_ftp_username" =>["storage_ftp_username","max:100"],
                "storage_ftp_password" =>["storage_ftp_password","max:100"],
                "storage_extensions" =>["storage_extensions","required|max:200"],
                "storage_max_size" =>["storage_max_size","required|max:10"],
                "storage_use_ftp" =>["storage_use_ftp","required|in:0,1"],
                
                // Color Configuration
                "color_primary" =>["color_primary","required|max:10"],
                "color_secondary" =>["color_secondary","required|max:10"],
                "color_body" =>["color_body","required|max:10"],
                
                // OnlyOffice Configuration
                "onlyoffice_url" =>["onlyoffice_url",""],
                "onlyoffice_secret" =>["onlyoffice_secret",""],
            ]);

            if ($v->passes()) {

                // App Configuration
                set_option("application_status",input("application_status"));
                set_option("public_status",input("public_status"));
                set_option("app_name",input("app_name"));

                // SMS Configuration
                set_option("sms_user_id",input("sms_user_id"));
                set_option("sms_password",input("sms_password"));
                set_option("sms_otp",input("sms_otp"));

                // reCAPTCHA Configuration
                set_option("recaptcha_active",input("recaptcha_active"));
                set_option("recaptcha_secret",input("recaptcha_secret"));
                set_option("recaptcha_site",input("recaptcha_site"));

                // Storage Configuration
                set_option("storage_path",input("storage_path"));
                set_option("storage_ftp_server",input("storage_ftp_server"));
                set_option("storage_ftp_username",input("storage_ftp_username"));
                set_option("storage_ftp_password",input("storage_ftp_password"));
                set_option("storage_extensions",input("storage_extensions"));
                set_option("storage_max_size",input("storage_max_size"));
                set_option("storage_use_ftp",input("storage_use_ftp"));

                // Color Configuration
                set_option("color_primary",input("color_primary"));
                set_option("color_secondary",input("color_secondary"));
                set_option("color_body",input("color_body"));

                // OnlyOffice Configuration
                set_option("onlyoffice_url",input("onlyoffice_url"));
                set_option("onlyoffice_secret",input("onlyoffice_secret"));

              
                set_option("email_reset_password",input("email_reset_password"));
                set_option("email_update_password",input("email_update_password"));
                set_option("login_attemp_sms",input("login_attemp_sms"));

                return _response([
                    "success"=>true,
                    "message"=>[tr("Updated successfully")]
                ]);

            }

            return _response([
                "success"=>false,
                "message"=>$v->errors()->all()
            ]);
        }


        $this->d['page']['title']=tr("Settings");
        $this->d['nav']=[
            "reload"=>true,
            "breadcrumb"=>[
                tr("Dashboard")=>base_url("admin"),
                tr("Settings")=>"",
            ],
            "active"=>"settings"
        ];


        $this->d['system_status'] = $this->getSystemStatus();
        $this->d['storage_config'] = $this->getStorageConfig();
        $this->d['color_config'] = $this->getColorConfig();
        $this->d['onlyoffice_config'] = $this->getOnlyOfficeConfig();

        return view("settings/index",$this->d);

    }

    /**
     * Get color configuration
     */
    private function getColorConfig()
    {
        return [
            'color_primary' => get_option('color_primary', '#27374D'),
            'color_secondary' => get_option('color_secondary', '#526D82'),
            'color_body' => get_option('color_body', '#EEEEEE'),
        ];
    }

    /**
     * Get OnlyOffice configuration
     */
    private function getOnlyOfficeConfig()
    {
        $config = [
            'onlyoffice_url' => get_option('onlyoffice_url', ''),
            'onlyoffice_secret' => get_option('onlyoffice_secret', ''),
        ];

        // Check OnlyOffice connection status if URL is configured
        if (!empty($config['onlyoffice_url'])) {
            $config['connection_status'] = $this->checkOnlyOfficeConnection($config['onlyoffice_url']);
        } else {
            $config['connection_status'] = ['status' => 'Not configured', 'class' => 'secondary'];
        }

        return $config;
    }

    /**
     * Check OnlyOffice connection
     */
    private function checkOnlyOfficeConnection($url)
    {
        if (empty($url)) {
            return ['status' => 'URL not provided', 'class' => 'warning'];
        }

        try {
            // Check if the OnlyOffice service is reachable
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'method' => 'GET'
                ]
            ]);
            
            $response = @file_get_contents($url . '/healthcheck', false, $context);
            
            if ($response !== false) {
                return ['status' => 'Connected', 'class' => 'success'];
            } else {
                return ['status' => 'Cannot connect to OnlyOffice server', 'class' => 'danger'];
            }
        } catch (\Exception $e) {
            return ['status' => 'Connection failed: ' . $e->getMessage(), 'class' => 'danger'];
        }
    }

    /**
     * Get system status information
     */
    private function getSystemStatus()
    {
        return [
            'php_version' => PHP_VERSION,
            'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
            'database_connection' => $this->checkDatabaseConnection(),
            'writable_directories' => $this->checkWritableDirectories(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'disk_space' => $this->getDiskSpace(),
            'extensions' => $this->checkRequiredExtensions()
        ];
    }

    /**
     * Check database connection
     */
    private function checkDatabaseConnection()
    {
        try {
            $db = \Config\Database::connect();
            $db->query('SELECT 1');
            return ['status' => 'Connected', 'class' => 'success'];
        } catch (\Exception $e) {
            return ['status' => 'Failed: ' . $e->getMessage(), 'class' => 'danger'];
        }
    }

    /**
     * Check writable directories
     */
    private function checkWritableDirectories()
    {
        $directories = [
            'writable/cache' => WRITEPATH . 'cache',
            'writable/logs' => WRITEPATH . 'logs',
            'writable/session' => WRITEPATH . 'session',
            'writable/uploads' => WRITEPATH . 'uploads'
        ];

        $results = [];
        foreach ($directories as $name => $path) {
            $results[$name] = [
                'writable' => is_writable($path),
                'exists' => is_dir($path),
                'class' => (is_writable($path) && is_dir($path)) ? 'success' : 'danger'
            ];
        }

        return $results;
    }

    /**
     * Get disk space information
     */
    private function getDiskSpace()
    {
        $bytes = disk_free_space(".");
        $si_prefix = array( 'B', 'KB', 'MB', 'GB', 'TB', 'EB', 'ZB', 'YB' );
        $base = 1024;
        $class = min((int)log($bytes , $base) , count($si_prefix) - 1);
        return sprintf('%1.2f' , $bytes / pow($base,$class)) . ' ' . $si_prefix[$class];
    }

    /**
     * Check required PHP extensions
     */
    private function checkRequiredExtensions()
    {
        $required = ['mysqli', 'mbstring', 'openssl', 'json', 'curl', 'gd', 'ftp'];
        $results = [];

        foreach ($required as $ext) {
            $results[$ext] = [
                'loaded' => extension_loaded($ext),
                'class' => extension_loaded($ext) ? 'success' : 'danger'
            ];
        }

        return $results;
    }

    /**
     * Get storage configuration and status
     */
    private function getStorageConfig()
    {
        $config = [
            'storage_path' => get_option('storage_path', env('storage.path', WRITEPATH . 'uploads')),
            'storage_ftp_server' => get_option('storage_ftp_server', env('storage.ftp_server', '')),
            'storage_ftp_username' => get_option('storage_ftp_username', env('storage.ftp_username', '')),
            'storage_ftp_password' => get_option('storage_ftp_password', env('storage.ftp_password', '')),
            'storage_extensions' => get_option('storage_extensions', env('storage.extensions', 'pdf,jpeg,png,jpg')),
            'storage_max_size' => get_option('storage_max_size', env('storage.max_size', '2M')),
            'storage_use_ftp' => get_option('storage_use_ftp', env('storage.use_ftp', '0')),
        ];

        // Check storage path accessibility
        $config['path_status'] = $this->checkStoragePath($config['storage_path']);
        
        // Check FTP connection if enabled
        if ($config['storage_use_ftp'] == '1') {
            $config['ftp_status'] = $this->checkFtpConnection(
                $config['storage_ftp_server'],
                $config['storage_ftp_username'],
                $config['storage_ftp_password']
            );
        } else {
            $config['ftp_status'] = ['status' => 'Disabled', 'class' => 'secondary'];
        }

        return $config;
    }

    /**
     * Check storage path accessibility
     */
    private function checkStoragePath($path)
    {
        if (!is_dir($path)) {
            return ['status' => 'Directory does not exist', 'class' => 'danger'];
        }
        
        if (!is_writable($path)) {
            return ['status' => 'Directory not writable', 'class' => 'warning'];
        }
        
        return ['status' => 'Accessible', 'class' => 'success'];
    }

    /**
     * Check FTP connection
     */
    private function checkFtpConnection($server, $username, $password)
    {
        if (empty($server) || empty($username) || empty($password)) {
            return ['status' => 'Missing credentials', 'class' => 'warning'];
        }

        if (!extension_loaded('ftp')) {
            return ['status' => 'FTP extension not loaded', 'class' => 'danger'];
        }

        try {
            $ftp_conn = ftp_connect($server);
            if (!$ftp_conn) {
                return ['status' => 'Cannot connect to FTP server', 'class' => 'danger'];
            }

            $login = ftp_login($ftp_conn, $username, $password);
            ftp_close($ftp_conn);

            if (!$login) {
                return ['status' => 'Authentication failed', 'class' => 'danger'];
            }

            return ['status' => 'Connected', 'class' => 'success'];
        } catch (\Exception $e) {
            return ['status' => 'Connection failed: ' . $e->getMessage(), 'class' => 'danger'];
        }
    }

    /**
     * Test storage connection with provided settings
     */
    private function testStorageConnection()
    {
        $useFtp = input('storage_use_ftp') == '1';
        $errors = [];
        $success = true;

        if ($useFtp) {
            // Test FTP connection
            $server = input('storage_ftp_server');
            $username = input('storage_ftp_username');
            $password = input('storage_ftp_password');

            if (empty($server) || empty($username) || empty($password)) {
                $errors[] = tr('FTP credentials are required');
                $success = false;
            } else {
                $ftpResult = $this->checkFtpConnection($server, $username, $password);
                if ($ftpResult['class'] !== 'success') {
                    $errors[] = $ftpResult['status'];
                    $success = false;
                }
            }
        } else {
            // Test local storage path
            $path = input('storage_path');
            if (empty($path)) {
                $errors[] = tr('Storage path is required');
                $success = false;
            } else {
                $pathResult = $this->checkStoragePath($path);
                if ($pathResult['class'] !== 'success') {
                    $errors[] = $pathResult['status'];
                    $success = false;
                }
            }
        }

        return _response([
            'success' => $success,
            'message' => $success ? tr('Storage connection test successful') : implode(', ', $errors)
        ]);
    }

}