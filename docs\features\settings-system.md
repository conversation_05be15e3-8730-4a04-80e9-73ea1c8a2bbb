# Settings System

## Overview
The settings system provides a comprehensive tabbed interface for managing all application configuration options. It includes sections for app configuration, SMS settings, templates, reCAPTCHA, and system status monitoring.

## Settings Helper Understanding

### How Settings Work
The settings system uses the `settings_helper.php` which provides two main functions:

#### `get_option($key, $default = null)`
- Retrieves a setting value from the database
- Returns the default value if the setting doesn't exist
- Caches results for performance

#### `set_option($key, $value)`
- Stores a setting value in the database
- Creates new setting if it doesn't exist
- Updates existing setting if it exists

### Database Storage
Settings are stored in the `options` table with:
- `option_name`: The setting key
- `option_value`: The setting value (stored as text)

## Tabbed Interface Structure

### 1. App Configuration Tab
**Purpose**: Core application settings

#### Settings:
- **app_name**: Application name displayed throughout the system
- **application_status**: Controls whether the application accepts new submissions
  - `Active`: Application is open for submissions
  - `Inactive`: Application is closed for submissions
- **public_status**: Controls public visibility of the application
  - `Active`: Application is publicly visible
  - `Inactive`: Application is hidden from public

#### Usage:
```php
// Get application name
$appName = get_option('app_name', 'Career Portal');

// Check if application is active
if (get_option('application_status') === 'Active') {
    // Allow submissions
}

// Check public visibility
if (get_option('public_status') === 'Active') {
    // Show to public
}
```

### 2. SMS Configuration Tab
**Purpose**: SMS service configuration

#### Settings:
- **sms_user_id**: SMS service provider user ID
- **sms_password**: SMS service provider password
- **sms_otp**: OTP SMS template with `{otp}` variable

#### Usage:
```php
// Get SMS credentials
$smsUserId = get_option('sms_user_id');
$smsPassword = get_option('sms_password');

// Get OTP template
$otpTemplate = get_option('sms_otp');
$message = str_replace('{otp}', $otpCode, $otpTemplate);
```

### 3. Templates Tab
**Purpose**: SMS and email templates

#### Settings:
- **email_reset_password**: Password reset SMS template
- **email_update_password**: Password updated SMS template
- **login_attemp_sms**: Login attempt notification SMS template
- **sms_{status}**: Application status SMS templates for each status

#### Available Variables:
- `{name}`: Applicant name
- `{card_id}`: Applicant card ID
- `{job_name}`: Job title
- `{otp}`: OTP code (for OTP template)

#### Usage:
```php
// Get reset password template
$resetTemplate = get_option('email_reset_password');

// Get application status template
$statusTemplate = get_option('sms_' . $applicationStatus);
$message = str_replace(['{name}', '{card_id}', '{job_name}'], 
                      [$name, $cardId, $jobName], $statusTemplate);
```

### 4. OnlyOffice Configuration Tab
**Purpose**: OnlyOffice Document Server integration settings

#### Settings:
- **onlyoffice_url**: URL of the OnlyOffice Document Server
- **onlyoffice_secret**: Secret key for JWT authentication (optional)

#### Setup Requirements:
1. Install OnlyOffice Document Server
2. Ensure server is accessible from the application
3. Configure HTTPS and JWT secret for production
4. Set up proper firewall and network configuration

#### Quick Setup for Testing:
```bash
# Using Docker
docker run -i -t -d -p 8080:80 onlyoffice/documentserver
```

#### Usage:
```php
// Get OnlyOffice configuration
$onlyOfficeUrl = get_option('onlyoffice_url', 'http://localhost:8080');
$onlyOfficeSecret = get_option('onlyoffice_secret', '');

// Check if OnlyOffice is configured
if (!empty($onlyOfficeUrl)) {
    // OnlyOffice integration available
}

// Verify JWT signature in callbacks
if ($onlyOfficeSecret) {
    $expectedSignature = base64_encode(hash_hmac('sha256', $data, $onlyOfficeSecret, true));
}
```

#### Supported File Types:
- **Text Documents**: DOC, DOCX, DOCM, DOT, DOTX, DOTM, ODT, FODT, OTT, RTF, TXT
- **Spreadsheets**: XLS, XLSX, XLSM, XLT, XLTX, XLTM, ODS, FODS, OTS, CSV
- **Presentations**: PPT, PPTX, PPTM, POT, POTX, POTM, ODP, FODP, OTP

#### Features:
- Real-time collaborative editing
- Document version tracking
- Integration with drive system
- Activity logging
- Connection status monitoring

### 5. Storage Configuration Tab
**Purpose**: File storage management

#### Settings:
- **storage_path**: Directory path for local file storage
- **storage_use_ftp**: Toggle between local and FTP storage (0/1)
- **storage_ftp_server**: FTP server hostname or IP
- **storage_ftp_username**: FTP authentication username
- **storage_ftp_password**: FTP authentication password
- **storage_extensions**: Allowed file extensions (comma-separated)
- **storage_max_size**: Maximum file size limit

#### Features:
- Local vs FTP storage options
- Real-time connection testing
- Status monitoring
- File validation settings

### 6. Colors Configuration Tab
**Purpose**: Application theme customization

#### Settings:
- **color_primary**: Primary theme color (hex)
- **color_secondary**: Secondary theme color (hex)
- **color_body**: Body background color (hex)

#### Features:
- Live color preview
- Color presets
- Hex color validation
- Visual color picker

### 7. reCAPTCHA Tab
**Purpose**: Google reCAPTCHA configuration

#### Settings:
- **recaptcha_active**: Enable/disable reCAPTCHA protection (1/0)
- **recaptcha_site**: Public site key from Google reCAPTCHA
- **recaptcha_secret**: Secret key from Google reCAPTCHA

#### Setup Instructions:
1. Visit [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. Register your domain
3. Copy the Site Key and Secret Key
4. Paste them in the settings form

#### Usage:
```php
// Check if reCAPTCHA is active
if (get_option('recaptcha_active') == '1') {
    // Include reCAPTCHA in forms
    $siteKey = get_option('recaptcha_site');
}

// Verify reCAPTCHA
$secretKey = get_option('recaptcha_secret');
// Verification logic here
```

### 8. System Status Tab
**Purpose**: System health monitoring (Read-only)

#### Information Displayed:
- **System Information**:
  - PHP Version
  - CodeIgniter Version
  - Server Software
  - Memory Limit
  - Max Execution Time
  - Upload Max Filesize
  - Post Max Size
  - Free Disk Space

- **Database Connection**: Connection status with color-coded alerts
- **PHP Extensions**: Status of required extensions (mysqli, mbstring, openssl, json, curl, gd)
- **Writable Directories**: Write permissions for cache, logs, session, uploads

## Controller Implementation

### Settings Controller Structure
```php
class Settings extends BaseController
{
    public function index()
    {
        // Handle form submission
        if (is_post()) {
            // Validate input
            // Save settings using set_option()
            // Return response
        }
        
        // Load system status
        $this->d['system_status'] = $this->getSystemStatus();
        
        // Return view
        return view("settings/index", $this->d);
    }
    
    private function getSystemStatus()
    {
        // Collect system information
        // Check database connection
        // Check writable directories
        // Check PHP extensions
        // Return status array
    }
}
```

### Validation Rules
```php
$v = validate([
    "application_status" => ["application_status","required|in:Active,Inactive"],
    "public_status" => ["public_status","required|in:Active,Inactive"],
    "app_name" => ["app_name","required|min:3|max:100"],
    "sms_user_id" => ["sms_user_id","max:100"],
    "sms_password" => ["sms_password","max:100"],
    "recaptcha_active" => ["recaptcha_active","required|in:0,1"],
    "recaptcha_secret" => ["recaptcha_secret","max:200"],
    "recaptcha_site" => ["recaptcha_site","max:200"],
    
    // Storage Configuration
    "storage_path" => ["storage_path","required|max:500"],
    "storage_ftp_server" => ["storage_ftp_server","max:200"],
    "storage_ftp_username" => ["storage_ftp_username","max:100"],
    "storage_ftp_password" => ["storage_ftp_password","max:100"],
    "storage_extensions" => ["storage_extensions","required|max:200"],
    "storage_max_size" => ["storage_max_size","required|max:10"],
    "storage_use_ftp" => ["storage_use_ftp","required|in:0,1"],
    
    // Color Configuration
    "color_primary" => ["color_primary","required|max:10"],
    "color_secondary" => ["color_secondary","required|max:10"],
    "color_body" => ["color_body","required|max:10"],
    
    // OnlyOffice Configuration
    "onlyoffice_url" => ["onlyoffice_url","permit_empty|valid_url|max:200"],
    "onlyoffice_secret" => ["onlyoffice_secret","permit_empty|max:200"],
]);
```

## User Interface Features

### Tab Navigation
- **Bootstrap Tabs**: Clean tabbed interface
- **Icons**: Font Awesome icons for each tab
- **Active State**: Remembers last active tab using localStorage
- **Responsive**: Works on mobile and desktop

### Form Features
- **AJAX Submission**: Form submits without page reload
- **Validation**: Real-time validation with error display
- **Input Groups**: Icons and proper styling
- **Help Text**: Descriptive text for each setting
- **Auto-reload**: Page reloads after successful save

### System Status Features
- **Color Coding**: Green for success, red for errors, yellow for warnings
- **Real-time**: Auto-refresh every 30 seconds when tab is active
- **Comprehensive**: Covers all critical system components

## JavaScript Implementation

### Tab Management
```javascript
// Remember active tab
var activeTab = localStorage.getItem('activeSettingsTab');
if (activeTab) {
    $('#settingsTabs a[href="' + activeTab + '"]').tab('show');
}

// Save active tab
$('#settingsTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
    localStorage.setItem('activeSettingsTab', $(e.target).attr('href'));
});
```

### Form Handling
```javascript
// Handle successful submission
$(document).on('submit_complete', function(event, data) {
    if (data.success) {
        setTimeout(function() {
            location.reload();
        }, 1500);
    }
});
```

### Auto-refresh
```javascript
// Auto-refresh system status
setInterval(function() {
    if ($('#system-status-tab').hasClass('active')) {
        $.get(window.location.href + '?refresh_status=1', function(data) {
            // Update system status content
        });
    }
}, 30000);
```

## Security Considerations

### Input Validation
- All inputs are validated server-side
- CSRF protection enabled
- XSS protection through proper escaping
- SQL injection protection through ORM

### Sensitive Data
- SMS passwords are input type="password"
- reCAPTCHA secret keys are input type="password"
- Settings are stored securely in database

### Access Control
- Requires `settings` permission
- Only administrators can access settings
- System status is read-only

## Performance Considerations

### Caching
- Settings are cached after first retrieval
- System status can be cached for short periods
- Tab state persisted in localStorage

### Database Optimization
- Settings table should have index on `option_name`
- Minimal database queries through caching
- Efficient system status collection

## Troubleshooting

### Common Issues

#### Settings Not Saving
- Check CSRF token
- Verify validation rules
- Check database connection
- Ensure proper permissions

#### System Status Errors
- Database connection issues
- File permission problems
- Missing PHP extensions
- Insufficient disk space

#### Tab Navigation Issues
- JavaScript errors in console
- Bootstrap CSS/JS not loaded
- localStorage restrictions

### Debug Mode
Enable debug mode to see detailed error information:
```php
// In controller
log_message('debug', 'Settings save attempt: ' . json_encode($input));
```

## Future Enhancements

### Potential Additions
- **Backup/Restore**: Export/import settings
- **Environment Variables**: Integration with .env files
- **Setting Groups**: Organize settings into logical groups
- **Setting History**: Track changes to settings
- **API Access**: REST API for settings management
- **Bulk Operations**: Import/export multiple settings

### Advanced Features
- **Setting Dependencies**: Settings that depend on others
- **Conditional Display**: Show/hide settings based on other values
- **Setting Validation**: Custom validation rules per setting
- **Setting Encryption**: Encrypt sensitive settings
- **Multi-tenant**: Different settings per tenant/organization
