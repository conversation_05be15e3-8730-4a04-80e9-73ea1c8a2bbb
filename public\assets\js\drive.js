/**
 * Drive System JavaScript
 * Handles file and directory operations
 */

$(document).ready(function() {
    // Initialize drive functionality
    initializeDrive();
});

function initializeDrive() {
    // Upload form handler
    $('#uploadForm').on('submit', function(e) {
        e.preventDefault();
        uploadFiles(this);
    });

    // Create folder form handler
    $('#createFolderForm').on('submit', function(e) {
        e.preventDefault();
        createFolder(this);
    });

    // View toggle handlers
    $('#viewGrid').on('click', function() {
        switchToGridView();
    });

    $('#viewList').on('click', function() {
        switchToListView();
    });

    // Drag and drop upload
    initializeDragDrop();
}

/**
 * Upload files
 */
function uploadFiles(form) {
    const formData = new FormData(form);
    const progressBar = $('#uploadProgress');
    const progressBarInner = progressBar.find('.progress-bar');

    progressBar.removeClass('d-none');
    progressBarInner.css('width', '0%');

    $.ajax({
        url: base_url + 'drive/files/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = (evt.loaded / evt.total) * 100;
                    progressBarInner.css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                $('#uploadModal').modal('hide');
                location.reload(); // Refresh to show new file
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Upload failed. Please try again.'], 'error');
        },
        complete: function() {
            progressBar.addClass('d-none');
            form.reset();
        }
    });
}



/**
 * Delete file
 */
function deleteFile(fileId) {
    if (!confirm('Are you sure you want to delete this file?')) {
        return;
    }

    $.ajax({
        url: base_url + 'drive/files/' + fileId + '/delete',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                $('[data-id="' + fileId + '"]').fadeOut();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to delete file. Please try again.'], 'error');
        }
    });
}

/**
 * Delete directory
 */
function deleteDirectory(directoryId) {
    if (!confirm('Are you sure you want to delete this directory?')) {
        return;
    }

    $.ajax({
        url: base_url + 'drive/directories/' + directoryId + '/delete',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                $('[data-id="' + directoryId + '"]').fadeOut();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to delete directory. Please try again.'], 'error');
        }
    });
}

/**
 * Rename file
 */
function renameFile(fileId, currentName) {
    const newName = prompt('Enter new file name:', currentName);
    if (!newName || newName === currentName) {
        return;
    }

    $.ajax({
        url: base_url + 'drive/files/' + fileId + '/rename',
        type: 'POST',
        data: { name: newName },
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                location.reload(); // Refresh to show new name
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to rename file. Please try again.'], 'error');
        }
    });
}

/**
 * Rename directory
 */
function renameDirectory(directoryId, currentName) {
    const newName = prompt('Enter new directory name:', currentName);
    if (!newName || newName === currentName) {
        return;
    }

    $.ajax({
        url: base_url + 'drive/directories/' + directoryId + '/rename',
        type: 'POST',
        data: { name: newName },
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                location.reload(); // Refresh to show new name
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to rename directory. Please try again.'], 'error');
        }
    });
}

/**
 * Share file
 */
function shareFile(fileId) {
    // Create share modal dynamically
    const shareModal = createShareModal('file', fileId);
    $('body').append(shareModal);
    $('#shareModal').modal('show');
}

/**
 * Share directory
 */
function shareDirectory(directoryId) {
    // Create share modal dynamically
    const shareModal = createShareModal('directory', directoryId);
    $('body').append(shareModal);
    $('#shareModal').modal('show');
}

/**
 * Create share modal
 */
function createShareModal(type, itemId) {
    return `
        <div class="modal fade" id="shareModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Share ${type} (Soon)</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <form id="shareForm">
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Recipient (User ID or Email)</label>
                                <input type="text" class="form-control" name="recipient" required>
                            </div>
                            <div class="form-group">
                                <label>Permission Level</label>
                                <select class="form-control" name="permission_level" required>
                                    <option value="VIEW">View Only</option>
                                    <option value="EDIT">Can Edit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Expires At (Optional)</label>
                                <input type="datetime-local" class="form-control" name="expires_at">
                            </div>
                        </div>
                        <div class="modal-footer">
                        
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
}

/**
 * Handle share form submission
 */
$(document).on('submit', '#shareForm', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const type = $('#shareModal .modal-title').text().includes('file') ? 'file' : 'directory';
    const itemId = getCurrentItemId(); // You'll need to implement this
    
    $.ajax({
        url: base_url + 'drive/share/' + type + '/' + itemId,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                $('#shareModal').modal('hide');
                if (response.data && response.data.share_url) {
                    prompt('Share URL:', response.data.share_url);
                }
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to share item. Please try again.'], 'error');
        }
    });
});

/**
 * Edit with OnlyOffice
 */
function editWithOnlyOffice(fileId) {
    // OnlyOffice integration will be implemented
    window.open(base_url + 'drive/onlyoffice/edit/' + fileId, '_blank');
}

/**
 * Switch to grid view
 */
function switchToGridView() {
    $('#viewGrid').addClass('active');
    $('#viewList').removeClass('active');
    // Implementation for grid view
}

/**
 * Switch to list view
 */
function switchToListView() {
    $('#viewList').addClass('active');
    $('#viewGrid').removeClass('active');
    // Implementation for list view
}

/**
 * Initialize drag and drop upload
 */
function initializeDragDrop() {
    const dropZone = $('.card-body');
    
    dropZone.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('drag-over');
    });
    
    dropZone.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
    });
    
    dropZone.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleDroppedFiles(files);
        }
    });
}

/**
 * Handle dropped files
 */
function handleDroppedFiles(files) {
    const formData = new FormData();
    
    for (let i = 0; i < files.length; i++) {
        formData.append('file', files[i]);
    }
    
    // Add current directory ID
    const currentDirectoryId = getCurrentDirectoryId();
    formData.append('directory_id', currentDirectoryId);
    
    // Upload files
    uploadFilesFormData(formData);
}

/**
 * Upload files using FormData
 */
function uploadFilesFormData(formData) {
    $.ajax({
        url: base_url + 'drive/files/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                location.reload();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Upload failed. Please try again.'], 'error');
        }
    });
}

/**
 * Get current directory ID
 */
function getCurrentDirectoryId() {
    return $('input[name="directory_id"]').val() || '';
}

/**
 * Show message
 */
function showMessage(messages, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const messageHtml = messages.map(msg => `<div class="alert ${alertClass}">${msg}</div>`).join('');
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.content').prepend(messageHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Clean up modals when hidden
$(document).on('hidden.bs.modal', '#shareModal', function() {
    $(this).remove();
});
